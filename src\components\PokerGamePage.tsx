import { But<PERSON> } from "@/components/ui/button"
import { Share, Heart, Play, Plus } from "lucide-react"
import Image from "next/image"

export default function PokerGamePage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white relative overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image src="/poker-chips-bg.png" alt="Poker chips background" fill className="object-cover opacity-60" />
        <div className="absolute inset-0 bg-gradient-to-r from-gray-900/95 via-gray-900/80 to-transparent" />
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-6 py-16">
        <div className="max-w-4xl">
          {/* Title Section */}
          <div className="mb-12">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              Mega Hit Poker:<br />
              Texas Holdem
            </h1>
            <div className="text-emerald-400 text-lg font-medium mb-2">Wonder People</div>
            <div className="text-gray-400 text-sm">Contains ads</div>
          </div>

          {/* App Info Section */}
          <div className="flex items-start gap-6 mb-10">
            {/* App Icon */}
            <div className="w-16 h-16 bg-gradient-to-br from-purple-600 via-pink-500 to-red-500 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg">
              <div className="text-white font-bold text-xs">MHP</div>
            </div>

            {/* Stats */}
            <div className="flex gap-8">
              <div className="text-left">
                <div className="flex items-center gap-1 mb-1">
                  <span className="text-xl font-bold">4.4</span>
                  <span className="text-yellow-400">★</span>
                </div>
                <div className="text-gray-400 text-xs">392K reviews</div>
              </div>

              <div className="text-left">
                <div className="text-xl font-bold mb-1">5M+</div>
                <div className="text-gray-400 text-xs">Downloads</div>
              </div>

              <div className="text-left">
                <div className="bg-gray-700 px-2 py-1 rounded text-xs font-bold mb-1 inline-block">18+</div>
                <div className="text-gray-400 text-xs">Rated for 18+</div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap items-center gap-4 mb-12">
            <Button className="bg-emerald-500 hover:bg-emerald-600 text-black font-semibold px-8 py-3 h-auto rounded-full">
              Install on Windows
            </Button>

            <Button
              variant="outline"
              className="border-gray-600 text-white hover:bg-gray-800 px-6 py-3 h-auto bg-transparent rounded-full"
            >
              Install on more devices
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="text-gray-300 hover:text-white hover:bg-gray-800 p-3 rounded-full"
            >
              <Share className="w-5 h-5" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="text-gray-300 hover:text-white hover:bg-gray-800 p-3 rounded-full"
            >
              <Plus className="w-5 h-5" />
            </Button>

            <div className="ml-auto">
              <Button
                variant="outline"
                className="border-gray-600 text-white hover:bg-gray-800 px-6 py-3 h-auto bg-transparent rounded-full"
              >
                <Play className="w-4 h-4 mr-2" />
                Trailer
              </Button>
            </div>
          </div>

          {/* Legal Text */}
          <div className="text-gray-400 text-sm leading-relaxed max-w-3xl">
            Google Play Games beta is required to install this game on Windows. By downloading the beta and the game,
            you agree to the{" "}
            <a href="#" className="text-emerald-400 underline hover:text-emerald-300">
              Google Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="text-emerald-400 underline hover:text-emerald-300">
              Google Play Terms of Service
            </a>
            .{" "}
            <a href="#" className="text-emerald-400 underline hover:text-emerald-300">
              Learn more
            </a>
            .
          </div>
        </div>
      </div>
    </div>
  )
}
